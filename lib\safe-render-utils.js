/**
 * Safe rendering utilities to prevent React Error #130
 * "Objects are not valid as a React child"
 */
import React from 'react';

/**
 * Safely render a value as a string, handling objects and null/undefined values
 * @param {any} value - The value to render
 * @param {string} fallback - Fallback value if rendering fails
 * @returns {string} - Safe string representation
 */
export const safeRender = (value, fallback = 'N/A') => {
  try {
    if (value === null || value === undefined) return fallback;

    if (typeof value === 'object') {
      // If it's an object, try to extract a meaningful value
      if (value.name) return String(value.name);
      if (value.value) return String(value.value);
      if (value.title) return String(value.title);
      if (value.label) return String(value.label);

      // Try toString if available
      if (value.toString && typeof value.toString === 'function') {
        const stringValue = value.toString();
        return stringValue === '[object Object]' ? fallback : stringValue;
      }

      // If it's an array, join the elements
      if (Array.isArray(value)) {
        return value.map(item => safeRender(item, '')).join(', ') || fallback;
      }

      return fallback;
    }

    return String(value);
  } catch (error) {
    console.error('Error rendering value:', error, 'Value:', value);
    return fallback;
  }
};

/**
 * Safely format currency with error handling
 * @param {number|object} amount - Amount to format
 * @param {string} currency - Currency code (default: AUD)
 * @param {string} locale - Locale for formatting (default: en-AU)
 * @returns {string} - Formatted currency string
 */
export const safeFormatCurrency = (amount, currency = 'AUD', locale = 'en-AU') => {
  try {
    if (amount == null || amount === undefined) return 'N/A';

    // Ensure amount is a number
    const numericAmount = typeof amount === 'object' ?
      (amount.value || amount.amount || 0) :
      parseFloat(amount);

    if (isNaN(numericAmount)) return 'N/A';

    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: currency,
    }).format(numericAmount);
  } catch (error) {
    console.error('Error formatting currency:', error, 'Amount:', amount);
    return 'N/A';
  }
};

/**
 * Safely format duration with error handling
 * @param {number|object} minutes - Duration in minutes
 * @returns {string} - Formatted duration string
 */
export const safeFormatDuration = (minutes) => {
  try {
    if (!minutes || minutes === null || minutes === undefined) return 'N/A';

    // Handle object case
    const numericMinutes = typeof minutes === 'object' ?
      (minutes.value || minutes.duration || 0) :
      parseInt(minutes, 10);

    if (isNaN(numericMinutes) || numericMinutes <= 0) return 'N/A';

    if (numericMinutes < 60) {
      return `${numericMinutes} mins`;
    }

    const hours = Math.floor(numericMinutes / 60);
    const remainingMins = numericMinutes % 60;

    if (remainingMins === 0) {
      return `${hours} hr${hours > 1 ? 's' : ''}`;
    }

    return `${hours} hr${hours > 1 ? 's' : ''} ${remainingMins} mins`;
  } catch (error) {
    console.error('Error formatting duration:', error, 'Minutes:', minutes);
    return 'N/A';
  }
};

/**
 * Safely format a date with error handling
 * @param {string|Date|object} date - Date to format
 * @param {object} options - Intl.DateTimeFormat options
 * @returns {string} - Formatted date string
 */
export const safeFormatDate = (date, options = {}) => {
  try {
    if (!date || date === null || date === undefined) return 'N/A';

    // Handle object case
    const dateValue = typeof date === 'object' && !(date instanceof Date) ?
      (date.value || date.date || date.created_at || date.updated_at) :
      date;

    if (!dateValue) return 'N/A';

    const dateObj = typeof dateValue === 'string' ? new Date(dateValue) : dateValue;

    if (isNaN(dateObj.getTime())) return 'N/A';

    const defaultOptions = {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    };

    return dateObj.toLocaleDateString('en-AU', { ...defaultOptions, ...options });
  } catch (error) {
    console.error('Error formatting date:', error, 'Date:', date);
    return 'N/A';
  }
};

/**
 * Safely serialize data for API responses to prevent object rendering errors
 * @param {any} data - Data to serialize
 * @returns {any} - Serialized data with primitive values only
 */
export const safeSerializeData = (data) => {
  try {
    if (data === null || data === undefined) return data;

    if (Array.isArray(data)) {
      return data.map(item => safeSerializeData(item));
    }

    if (typeof data === 'object' && data.constructor === Object) {
      const serialized = {};
      for (const [key, value] of Object.entries(data)) {
        if (value === null || value === undefined) {
          serialized[key] = value;
        } else if (typeof value === 'object' && value.constructor === Object) {
          // For nested objects, try to extract meaningful values
          serialized[key] = safeSerializeData(value);
        } else if (Array.isArray(value)) {
          serialized[key] = value.map(item => safeSerializeData(item));
        } else if (typeof value === 'function') {
          // Skip functions
          continue;
        } else {
          // Convert to primitive type
          serialized[key] = typeof value === 'object' ? String(value) : value;
        }
      }
      return serialized;
    }

    // For primitive types, return as-is
    return data;
  } catch (error) {
    console.error('Error serializing data:', error, 'Data:', data);
    return null;
  }
};

/**
 * Validate that data is safe for React rendering
 * @param {any} data - Data to validate
 * @returns {boolean} - True if safe to render
 */
export const isRenderSafe = (data) => {
  try {
    if (data === null || data === undefined) return true;
    if (typeof data === 'string' || typeof data === 'number' || typeof data === 'boolean') return true;
    if (React.isValidElement(data)) return true;

    // Objects and arrays are not safe to render directly
    return false;
  } catch (error) {
    console.error('Error validating render safety:', error, 'Data:', data);
    return false;
  }
};
