import Head from 'next/head'
import Link from 'next/link'
import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import styles from '@/styles/Services.module.css'
import Layout from '@/components/Layout'
import ServicesHeroShowcase from '@/components/ServicesHeroShowcase'
import { safeRender } from '@/lib/safe-render-utils'

import { supabase } from '@/lib/supabase'

export default function Services() {
  const [isAdmin, setIsAdmin] = useState(false);
  const [services, setServices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const router = useRouter();

  // Fetch services from database
  useEffect(() => {
    const fetchServices = async () => {
      try {
        setLoading(true);
        setError(null);
        console.log('Fetching services from API...');

        const response = await fetch('/api/public/services');
        console.log('Services API response status:', response.status);

        if (!response.ok) {
          const errorText = await response.text();
          console.error('Services API error response:', errorText);
          throw new Error(`Failed to fetch services: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        console.log('Services API data received:', data);

        if (data && data.services && Array.isArray(data.services)) {
          setServices(data.services);
          console.log(`Successfully loaded ${data.services.length} services`);
        } else {
          console.warn('Invalid services data structure:', data);
          setServices([]);
        }
      } catch (err) {
        console.error('Error fetching services:', err);
        setError(err.message);
        // Fallback to empty array if fetch fails
        setServices([]);
      } finally {
        setLoading(false);
      }
    };

    fetchServices();
  }, []);

  // Check if user is admin
  useEffect(() => {
    const checkAdminStatus = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession();
        if (session?.user) {
          // Check if user has admin role
          const { data: profile } = await supabase
            .from('user_profiles')
            .select('role')
            .eq('id', session.user.id)
            .single();

          setIsAdmin(profile?.role === 'admin');
        }
      } catch (error) {
        console.error('Error checking admin status:', error);
      }
    };

    checkAdminStatus();
  }, []);

  const handleEditService = (serviceId) => {
    router.push(`/admin/inventory?tab=services&edit=${serviceId}`);
  };

  const handleBookService = (service) => {
    // Navigate to book-online page with service pre-selected
    if (service?.id) {
      const serviceParam = encodeURIComponent(service.id);
      window.location.href = `/book-online?service=${serviceParam}`;
    } else {
      console.error('Invalid service ID for booking:', service);
    }
  };



  // Format services for the hero component with safe rendering
  const heroServices = services.map(service => ({
    title: service?.title || 'Service',
    icon: service?.icon || '🎨',
    color: service?.accentColor || '#4ECDC4',
    image: service?.image || '/images/services/face-paint.jpg'
  }));

  // Show loading state
  if (loading) {
    return (
      <Layout>
        <Head>
          <title>Services | OceanSoulSparkles</title>
          <meta name="description" content="Explore our range of services including face painting, airbrush body art, and braiding for events, festivals, and parties." />
        </Head>
        <main className={styles.main}>
          <div className={styles.loadingContainer}>
            <div className={styles.loadingSpinner}></div>
            <p>Loading our magical services...</p>
          </div>
        </main>
      </Layout>
    );
  }

  // Show error state
  if (error) {
    return (
      <Layout>
        <Head>
          <title>Services | OceanSoulSparkles</title>
          <meta name="description" content="Explore our range of services including face painting, airbrush body art, and braiding for events, festivals, and parties." />
        </Head>
        <main className={styles.main}>
          <div className={styles.errorContainer}>
            <h2>Oops! Something went wrong</h2>
            <p>We're having trouble loading our services. Please try refreshing the page.</p>
            <button onClick={() => window.location.reload()} className={styles.retryButton}>
              Try Again
            </button>
          </div>
        </main>
      </Layout>
    );
  }

  return (
    <Layout>
      <Head>
        <title>Services | OceanSoulSparkles</title>
        <meta name="description" content="Explore our range of services including face painting, airbrush body art, and braiding for events, festivals, and parties." />
      </Head>

      <main className={styles.main}>
        {/* Hero Showcase with Floating Service Images */}
        <ServicesHeroShowcase
          title="Our Magical Services"
          subtitle="Transform your events with our creative and mesmerizing services"
          backgroundImage="/images/services-hero.jpg"
          services={heroServices}
          ctaText="Book Your Experience"
          ctaLink="/book-online"
        />

        {/* Service Cards Section */}
        <section className={styles.serviceCardsSection}>
          <div className={styles.sectionContainer}>
            <h2 className={styles.sectionTitle}>Our Services</h2>
            <p className={styles.sectionSubtitle}>
              Discover our range of creative services designed to make your event unforgettable
            </p>

            {/* Service cards */}
            <div className={styles.cardsGrid}>
              {services.map((service) => (
                <div
                  key={service?.id || `service-${Math.random()}`}
                  className={styles.serviceCard}
                >
                  <div className={styles.cardContent}>
                    {/* Card front - Full image with title overlay */}
                    <div className={styles.cardFront}>
                      <div className={styles.imageContainer}>
                        <img
                          src={service?.image || '/images/services/face-paint.jpg'}
                          alt={service?.title || 'Service'}
                          className={styles.serviceImage}
                        />
                        <div
                          className={styles.imageOverlay}
                          style={{ backgroundColor: service?.accentColor || '#4ECDC4' }}
                        ></div>
                        <div className={styles.serviceTitleOverlay}>
                          <h3 className={styles.serviceTitle}>{service?.title || 'Service'}</h3>
                          {isAdmin && (
                            <button
                              onClick={() => handleEditService(service?.id)}
                              className={styles.adminEditButton}
                              title="Edit Service"
                            >
                              ✏️ Edit
                            </button>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Card back - Service details and pricing */}
                    <div className={styles.cardExpanded}>
                      <h3 className={styles.pricingTitle}>{service?.title || 'Service'}</h3>
                      <p className={styles.serviceDescription}>{service?.description || ''}</p>
                      <ul className={styles.pricingList}>
                        {(service?.pricing || []).map((item, idx) => (
                          <li key={idx} className={styles.pricingItem}>
                            <span className={styles.pricingItemTitle}>{item?.title || 'Price'}</span>
                            <span className={styles.pricingItemPrice}>{item?.price || 'N/A'}</span>
                          </li>
                        ))}
                      </ul>
                      <button
                        onClick={() => handleBookService(service)}
                        className={styles.bookButton}
                      >
                        Book Now
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Gallery Link Section */}
        <section className={styles.galleryLinkSection}>
          <div className={styles.sectionContainer}>
            <h2 className={styles.sectionTitle}>Explore Our Work</h2>
            <p className={styles.sectionSubtitle}>
              Browse our gallery to see examples of our services and get inspired for your next event
            </p>
            <div className={styles.galleryLinkContainer}>
              <Link href="/gallery" className={styles.galleryLink}>
                View Gallery
              </Link>
            </div>
          </div>
        </section>

        {/* Booking Information Section */}
        <section className={styles.bookingInfo}>
          <h2 className={styles.sectionTitle}>Booking Information</h2>
          <p className={styles.sectionSubtitle}>
            Everything you need to know about booking our services for your next event
          </p>
          <div className={styles.infoGrid}>
            <div className={styles.infoCard}>
              <h3>Event Services</h3>
              <p>
                We offer special packages for events of all sizes, from intimate gatherings to large festivals.
                Our team can accommodate multiple services at once to ensure all your guests get the full experience.
              </p>
            </div>
            <div className={styles.infoCard}>
              <h3>Travel Information</h3>
              <p>
                We service the greater Melbourne area. Travel fees may apply for locations outside a 20km radius
                from Melbourne CBD. Please inquire for specific details when booking.
              </p>
            </div>
            <div className={styles.infoCard}>
              <h3>Booking Process</h3>
              <p>
                To secure your booking, we require a 50% deposit. The remaining balance is due on the day of the event.
                We accept various payment methods including bank transfer, PayPal, and credit card.
              </p>
            </div>
            <div className={styles.infoCard}>
              <h3>Cancellation Policy</h3>
              <p>
                Cancellations made more than 7 days before the event will receive a full refund of the deposit.
                Cancellations within 7 days of the event will forfeit the deposit. Rescheduling is available subject to availability.
              </p>
            </div>
          </div>
        </section>

        {/* Call to Action Section */}
        <section className={styles.cta}>
          <h2>Ready to add some sparkle to your event?</h2>
          <p>Contact us today to discuss your needs and get a custom quote.</p>
          <div className={styles.ctaButtons}>
            <Link href="/book-online" className={styles.button}>
              Book Online
            </Link>
            <Link href="/contact" className={`${styles.button} ${styles.outlineButton}`}>
              Contact Us
            </Link>
          </div>
        </section>

        {/* Admin Panel Link */}
        {isAdmin && (
          <div className={styles.adminPanelLink}>
            <Link href="/admin/inventory?tab=services" className={styles.adminButton}>
              ⚙️ Manage Services
            </Link>
          </div>
        )}
      </main>


    </Layout>
  );
}
